# Instructions

During your interaction with the user, if you find anything reusable in this project (e.g. version of a library, model name), especially about a fix to a mistake you made or a correction you received, you should take note in the `Lessons` section in the `.cursorrules` file so you will not make the same mistake again.

You should also use the `.cursorrules` file as a Scratchpad to organize your thoughts. Especially when you receive a new task, you should first review the content of the Scratchpad, clear old different task if necessary, first explain the task, and plan the steps you need to take to complete the task. You can use todo markers to indicate the progress, e.g.
[X] Task 1
[ ] Task 2

Also update the progress of the task in the Scratchpad when you finish a subtask.
Especially when you finished a milestone, it will help to improve your depth of task accomplishment to use the Scratchpad to reflect and plan.
The goal is to help you maintain a big picture as well as the progress of the task. Always refer to the Scratchpad when you plan the next step.

# Lessons

## User Specified Lessons

- Include info useful for debugging in the program output.
- Read the file before you try to edit it.

## Cline learned

- For search results, ensure proper handling of different character encodings (UTF-8) for international queries
- Add debug information to stderr while keeping the main output clean in stdout for better pipeline integration

# Scratchpad

## Current Task: Check Ad Configuration and IAP Integration

Task: Check if interstitial and app open ads are configured correctly, and verify if the IAP payment system can handle disabling these ads when a user has paid.

### Plan:

[X] Examine interstitial ads implementation (src/Components/ads/Interstitials.js)
[X] Examine app open ads implementation (src/Components/hook/useAppOpen.js)
[X] Examine IAP service implementation (src/services/iapService.js)
[X] Examine IAP hook implementation (src/services/useIAP.js)
[X] Check how ads are disabled when a user has paid (Settings.js and other relevant files)
[X] Verify the connection between IAP and ad disabling
[X] Summarize findings and identify any issues or improvements needed

### Findings:

#### Interstitial Ads (src/Components/ads/Interstitials.js)

- Uses Google Mobile Ads (react-native-google-mobile-ads)
- Ad unit ID is fetched from Firebase Remote Config
- Has fallback ad unit ID if the main one fails
- Implements retry logic with exponential backoff
- Shows ads based on a boolean flag (showAd)
- No direct check for IAP status before showing ads

#### App Open Ads (src/Components/hook/useAppOpen.js)

- Uses Google Mobile Ads AppOpenAd
- Ad unit ID is fetched from Firebase Remote Config
- Checks IAP status before initializing ads via iapService.isAdFree()
- Only initializes ads if user doesn't have ad-free status
- Revalidates subscription status when app comes to foreground

#### IAP Implementation (iapService.js and useIAP.js)

- Uses react-native-iap for in-app purchases
- Supports two products:
  - REMOVE_ADS_Subscription: Recurring subscription for ad-free experience
  - ONE_MONTH_AD_FREE: One-time purchase for 30 days ad-free
- Properly validates purchases with getAvailablePurchases()
- Checks expiry dates for subscriptions and one-time purchases
- Exposes isAdFree() method to check if ads should be disabled

#### Integration Points

- Settings.js: Shows IAP purchase options and updates UI based on subscription status
- useAppOpen.js: Directly checks IAP status before showing app open ads
- ReconnectButton.js: Shows interstitial ads based on user interaction count, but doesn't check IAP status

### Issues Identified

1. **Missing IAP Check in Interstitial Ads**: The interstitial ads implementation (Interstitials.js) doesn't check if the user has paid for ad-free experience before showing ads. This is a critical issue as users who have paid may still see interstitial ads.

2. **Inconsistent Ad Disabling**: App open ads properly check for IAP status, but interstitial ads don't. This creates an inconsistent user experience where some ads are disabled after purchase while others still appear.

3. **No IAP Check in ReconnectButton**: The ReconnectButton component shows interstitial ads based on user interaction count but doesn't check if the user has paid for ad-free experience.

4. **No Initialization Check in App.js**: The App.js file calls `initializeInterstitialAds()` without checking if the user has an active ad-free subscription first.

### Recommended Improvements

1. **Add IAP Check to Interstitial Ads**: Modify the `showInterstitial()` function in Interstitials.js to check if the user has paid for ad-free experience before showing ads:

```javascript
export function showInterstitial() {
  try {
    // First check if user has ad-free status
    iapService.isAdFree().then(isAdFree => {
      if (isAdFree) {
        // User has paid, skip ad and just reward
        rewardUser();
      } else if (showAd) {
        // Show ad only if user hasn't paid and ad is loaded
        interstitial.show();
        showAd = false;
      } else {
        rewardUser();
      }
    });
  } catch (e) {
    console.log(e);
    // On error, default to rewarding user
    rewardUser();
  }
}
```

2. **Add IAP Check to ReconnectButton**: Modify the `reconnect()` function in ReconnectButton.js to check if the user has paid before showing ads:

```javascript
const reconnect = () => {
  const AdInterval = remoteConfig().getValue('AdInterval').asNumber();
  analytics().logEvent('next_user');
  const partnersLenght = session.partners.length;

  // First check if user has ad-free status
  iapService
    .isAdFree()
    .then(isAdFree => {
      if (isAdFree) {
        // User has paid, skip ad
        SocketService.emit('reconnect');
      } else if (
        partnersLenght !== 0 &&
        Math.ceil(partnersLenght / 2) % AdInterval === 0
      ) {
        SocketService.emit('end');
        showInterstitial();
      } else {
        SocketService.emit('reconnect');
      }
    })
    .catch(error => {
      // On error, default to normal behavior
      console.error('Error checking ad-free status:', error);
      if (
        partnersLenght !== 0 &&
        Math.ceil(partnersLenght / 2) % AdInterval === 0
      ) {
        SocketService.emit('end');
        showInterstitial();
      } else {
        SocketService.emit('reconnect');
      }
    });
};
```

3. **Add IAP Check to App.js**: Modify the initialization in App.js to check IAP status before initializing interstitial ads:

```javascript
remoteConfig()
  .ensureInitialized()
  .finally(() => {
    // Check IAP status before initializing ads
    import('./src/services/iapService').then(({iapService}) => {
      iapService
        .isAdFree()
        .then(isAdFree => {
          if (!isAdFree) {
            // Only initialize ads if user hasn't paid
            initializeInterstitialAds();
          }
        })
        .catch(error => {
          console.error('Error checking ad-free status:', error);
          // On error, default to showing ads
          initializeInterstitialAds();
        });
    });
  });
```

4. **Centralize Ad-Free Status**: Consider creating a central ad manager that maintains the current ad-free status and provides a consistent interface for all ad types. This would ensure consistent behavior across different ad implementations.
