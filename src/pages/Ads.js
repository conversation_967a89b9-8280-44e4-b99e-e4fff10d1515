import React, {useCallback, useEffect, useState} from 'react';
import {BackHandler, StyleSheet, View, Text, Dimensions} from 'react-native';
import {useFocusEffect} from '@react-navigation/native';
import {ActivityIndicator} from 'react-native-paper';
import analytics from '@react-native-firebase/analytics';
import {BannerAd, BannerAdSize, TestIds} from 'react-native-google-mobile-ads';
import remoteConfig from '@react-native-firebase/remote-config';

const {width: screenWidth} = Dimensions.get('window');

export default function Ads() {
  const [adLoaded, setAdLoaded] = useState(false);
  const [adError, setAdError] = useState(false);

  analytics().logEvent('ads_tab_viewed');
  

  return (
    <View style={styles.container}>
      {!adLoaded && !adError && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size={50} color="#a9a9a9" />
          <Text style={styles.loadingText}>Loading ad...</Text>
        </View>
      )}

      {adError && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Unable to load ad</Text>
          <Text style={styles.errorSubtext}>Please try again later</Text>
        </View>
      )}

      <BannerAd
        unitId={__DEV__ ? TestIds.BANNER : remoteConfig().getValue('AdBanner').asString()}
        size={BannerAdSize.WIDE_SKYSCRAPER}
        requestOptions={{
          requestNonPersonalizedAdsOnly: false,
        }}
        onAdLoaded={() => {
          console.log('Banner ad loaded');
          setAdLoaded(true);
          setAdError(false);
        }}
        onAdFailedToLoad={(error) => {
          console.log('Banner ad failed to load:', error);
          setAdError(true);
          setAdLoaded(false);
        }}
        style={styles.bannerAd}
      />

      {/* Add multiple banner ads to fill more screen space */}
      {adLoaded && (
        <>
          <View style={styles.spacer} />
          <BannerAd
            unitId={__DEV__ ? TestIds.BANNER : remoteConfig().getValue('AdBanner').asString()}
            size={BannerAdSize.LARGE_BANNER}
            requestOptions={{
              requestNonPersonalizedAdsOnly: false,
            }}
            style={styles.secondaryBannerAd}
          />
          <View style={styles.spacer} />
          <BannerAd
            unitId={__DEV__ ? TestIds.BANNER : remoteConfig().getValue('AdBanner').asString()}
            size={BannerAdSize.MEDIUM_RECTANGLE}
            requestOptions={{
              requestNonPersonalizedAdsOnly: false,
            }}
            style={styles.secondaryBannerAd}
          />
        </>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContainer: {
    position: 'absolute',
    height: '100%',
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666666',
  },
  errorContainer: {
    position: 'absolute',
    height: '100%',
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
  },
  errorText: {
    fontSize: 18,
    color: '#FF6B6B',
    fontWeight: 'bold',
    marginBottom: 8,
  },
  errorSubtext: {
    fontSize: 14,
    color: '#666666',
  },
  adFreeContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 20,
  },
  adFreeText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#4CAF50',
    marginBottom: 10,
    textAlign: 'center',
  },
  adFreeSubtext: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
  },
  bannerAd: {
    width: screenWidth,
    alignSelf: 'center',
    marginVertical: 10,
  },
  secondaryBannerAd: {
    width: screenWidth,
    alignSelf: 'center',
  },
  spacer: {
    height: 20,
    backgroundColor: '#F5F5F5',
    width: '100%',
  },
});
