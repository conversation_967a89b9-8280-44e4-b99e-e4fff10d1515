import React from 'react';
import {
  StyleSheet,
  ImageBackground,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';

const BackgroundSection = props => {
  return (
    <ImageBackground
      source={require('../assets/images/background.png')}
      imageStyle={styles.imageStyle}
      style={styles.container}>
      <SafeAreaView style={styles.container}>
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'null'}
          style={styles.container}
          keyboardVerticalOffset={Platform.select({ios: 0, android: 500})}>
          {props.children}
        </KeyboardAvoidingView>
      </SafeAreaView>
    </ImageBackground>
  );
};
const styles = StyleSheet.create({
  container: {flex: 1, paddingBottom: 5},
  imageStyle: {opacity: 0.5},
});

export default BackgroundSection;
