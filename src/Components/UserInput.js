import React, {Component} from 'react';
import {
  StyleSheet,
  View,
  TextInput,
  TouchableOpacity,
  Platform,
  Keyboard,
} from 'react-native';
import Colors from '../Constants/Colors';
import ActionInputMenu from './Messages/Menu';
import Camera from '../assets/images/camera.svg';
import Photos from '../assets/images/photos.svg';
import Send from '../assets/images/send.svg';
import Smile from '../assets/images/smiley-svgrepo-com.svg';
import OpenMenu from '../assets/images/openMenu.svg';
import CloseMenu from '../assets/images/closeMenu.svg';
import {Stage} from '../store/tooltip/enum';
import Icon from './InputSection/Icon';
import analytics from '@react-native-firebase/analytics';
export default class UserInput extends Component {
  sendText = () => {
    if (this.props.value) {
      // Call onSubmit immediately for better responsiveness
      this.props.onSubmit();

      // Log analytics event after UI update
      setTimeout(() => {
        analytics().logEvent('send_message');
      }, 0);
    }
  };

  onClick = emoji => {
    this.props.insertEmoji(emoji);
    // console.log(emoji);
  };
  onEmojiIconClicked = () => {
    if (!this.props.showEmoji) {
      Keyboard.dismiss();
    }
    this.props.setShowEmoji(!this.props.showEmoji);
  };

  menuAnchor = visible => {
    return visible ? (
      <CloseMenu fill={Colors.iconColor} width={25} height={25} />
    ) : (
      <OpenMenu fill={Colors.iconColor} width={25} height={25} />
    );
  };
  render() {
    return (
      <View style={styles.container}>
        <View style={styles.iconWrapper}>
          <ActionInputMenu
            menuAnchor={this.menuAnchor}
            menuItems={this.props.menuItems}
          />
        </View>
        <Icon
          render={!this.props.value}
          onPress={this.props.onGaralryPick}
          stage={Stage.Galary}
          nextStage={Stage.Menu}
          tooltipText="Click to choose a photo from galary">
          <Photos fill={Colors.iconColor} width={20} height={20} />
        </Icon>
        <Icon
          render={!this.props.value}
          onPress={this.props.onCameraOpen}
          stage={Stage.Camera}
          nextStage={Stage.Galary}
          tooltipText="Click to send a photo using your camera">
          <Camera fill={Colors.iconColor} width={20} height={20} />
        </Icon>
        <Icon
          render
          onPress={this.onEmojiIconClicked}
          attachIconStyle={styles.emojiIcon}>
          <Smile fill={Colors.iconColor} width={20} height={20} />
        </Icon>
        <View style={styles.inputWrapper}>
          <TextInput
            style={[styles.input]}
            placeholderTextColor={'gray'}
            placeholder={this.props.placeholder}
            returnKeyType={this.props.returnKeyType}
            onChangeText={this.props.onChangeText}
            multiline={true}
            blurOnSubmit
            maxLength={1000} // Limit text length for better performance
            autoCapitalize="sentences"
            autoCorrect={true}
            onSelectionChange={event =>
              event.nativeEvent.selection.end ===
                event.nativeEvent.selection.start &&
              this.props.setCursorPosition(event.nativeEvent.selection.start)
            }
            value={this.props.value}
            onSubmitEditing={this.sendText}
            onFocus={() => {
              this.props.setShowEmoji(false);
            }}
          />
          {this.props.value !== '' && (
            <TouchableOpacity
              style={styles.sendButton}
              onPress={this.sendText}
              activeOpacity={0.6}
              hitSlop={{top: 10, bottom: 10, left: 10, right: 10}}
              delayPressIn={0}>
              <View style={styles.sendButtonInner}>
                <Send fill={'white'} width={20} height={20} />
              </View>
            </TouchableOpacity>
          )}
        </View>
      </View>
    );
  }
}

const styles = StyleSheet.create({
  input: {
    flex: 1,
    fontFamily:
      Platform.OS === 'ios' ? 'Poppins-Regular' : 'PoppinsRegular-B2Bw',
    backgroundColor: Colors.textInputBackground,
    borderRadius: 20,
    fontSize: 17,
    paddingRight: 50,
    paddingLeft: 15,
    marginRight: 5,
    color: 'black',
  },
  inputWrapper: {
    flex: 12,
    flexDirection: 'row',
    alignItems: 'center',
  },
  container: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 5,
  },
  sendButton: {
    zIndex: 99,
    position: 'absolute',
    right: 15,
    height: 36,
    width: 36,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButtonInner: {
    width: 36,
    height: 36,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
    elevation: 2,
  },
  emojiIcon: {paddingRight: 10},
  iconWrapper: {paddingHorizontal: 3},
});
