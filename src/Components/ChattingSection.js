import React, {useRef, useEffect} from 'react';
import {Animated, FlatList, StyleSheet, View} from 'react-native';
import {useRecoilValue} from 'recoil';
import {messageSelector} from '../store/session/selectors';
import ChatMessage from './ChatMessage';

const ChattingSection = props => {
  const messages = useRecoilValue(messageSelector);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  
  // Stable key extractor to prevent remounting issues
  const keyExtractor = React.useCallback((item, index) => {
    return item.id ? item.id.toString() : `message-${index}`;
  }, []);

  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, [fadeAnim]);

  const renderItem = ({item, index}) => {
    // Simplified rendering without per-item animations to prevent mounting conflicts
    const messageComponent = item.source ? (
      <ChatMessage item={item} />
    ) : (
      <ChatMessage
        item={{...item, name: item.name, gender: item.gender, avatar: item.avatar}}
      />
    );

    return (
      <Animated.View style={{opacity: fadeAnim}}>
        {messageComponent}
      </Animated.View>
    );
  };

  return (
    <View style={[props.style, styles.container]}>
      <FlatList
        inverted
        data={messages}
        renderItem={renderItem}
        keyExtractor={keyExtractor}
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
        initialNumToRender={10}
        maxToRenderPerBatch={5}
        windowSize={5}
        removeClippedSubviews={false}
        getItemLayout={null}
        maintainVisibleContentPosition={{
          minIndexForVisible: 0,
          autoscrollToTopThreshold: 10,
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  listContent: {
    paddingVertical: 16,
    paddingHorizontal: 8,
  },
});

export default ChattingSection;
