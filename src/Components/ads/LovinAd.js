// import React from 'react';
// import {ActivityIndicator} from 'react-native-paper';
// import AppLovinMAX from 'react-native-applovin-max';
// import {LovinBanner} from '../../../app.json';
// import {StyleSheet} from 'react-native';
// import {useState, useEffect} from 'react';

// export default function LovinAd() {
//   const [adIntialized, setAdIntialized] = useState(AppLovinMAX.isInitialized());
//   const [adloading, setAdloading] = useState(true);
//   useEffect(() => {
//     if (!adIntialized) {
//       var timer = setInterval(function () {
//         if (AppLovinMAX.isInitialized()) {
//           setAdIntialized(true);
//           clearInterval(timer);
//         }
//       }, 100);
//       setTimeout(() => {
//         if (!adIntialized) {
//           setAdloading(false);
//           clearInterval(timer);
//         }
//       }, 60000);
//     }
//   });
//   return (
//     adloading &&
//     (adIntialized ? (
//       <AppLovinMAX.AdView
//         adUnitId={LovinBanner}
//         adFormat={AppLovinMAX.AdFormat.BANNER}
//         style={styles.banner}
//       />
//     ) : (
//       <ActivityIndicator size={28} color="#a9a9a9" />
//     ))
//   );
// }
// const styles = StyleSheet.create({
//   banner: {width: '100%'},
// });
