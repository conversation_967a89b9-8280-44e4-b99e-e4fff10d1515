import {MD3LightTheme, configureFonts} from 'react-native-paper';
import {Platform} from 'react-native';

const baseFont = Platform.select({
  ios: 'Poppins-Regular',
  android: 'PoppinsRegular-B2Bw',
});

const baseFontBold = Platform.select({
  ios: 'Poppins-Bold',
  android: 'PoppinsBold-GdJA',
});

const fontConfig = {
  displayLarge: {
    ...MD3LightTheme.fonts.displayLarge,
    fontFamily: baseFontBold,
  },
  displayMedium: {
    ...MD3LightTheme.fonts.displayMedium,
    fontFamily: baseFontBold,
  },
  displaySmall: {
    ...MD3LightTheme.fonts.displaySmall,
    fontFamily: baseFontBold,
  },
  headlineLarge: {
    ...MD3LightTheme.fonts.headlineLarge,
    fontFamily: baseFontBold,
  },
  headlineMedium: {
    ...MD3LightTheme.fonts.headlineMedium,
    fontFamily: baseFontBold,
  },
  headlineSmall: {
    ...MD3LightTheme.fonts.headlineSmall,
    fontFamily: baseFontBold,
  },
  titleLarge: {
    ...MD3LightTheme.fonts.titleLarge,
    fontFamily: baseFontBold,
  },
  titleMedium: {
    ...MD3LightTheme.fonts.titleMedium,
    fontFamily: baseFontBold,
  },
  titleSmall: {
    ...MD3LightTheme.fonts.titleSmall,
    fontFamily: baseFontBold,
  },
  bodyLarge: {
    ...MD3LightTheme.fonts.bodyLarge,
    fontFamily: baseFont,
  },
  bodyMedium: {
    ...MD3LightTheme.fonts.bodyMedium,
    fontFamily: baseFont,
  },
  bodySmall: {
    ...MD3LightTheme.fonts.bodySmall,
    fontFamily: baseFont,
  },
  labelLarge: {
    ...MD3LightTheme.fonts.labelLarge,
    fontFamily: baseFont,
  },
  labelMedium: {
    ...MD3LightTheme.fonts.labelMedium,
    fontFamily: baseFont,
  },
  labelSmall: {
    ...MD3LightTheme.fonts.labelSmall,
    fontFamily: baseFont,
  },
};

export const theme = {
  ...MD3LightTheme,
  fonts: configureFonts({config: fontConfig}),
  colors: {
    ...MD3LightTheme.colors,
    primary: '#2196f3',
    secondary: '#5276AC',
    tertiary: '#f7941d',
    background: '#FFFFFF',
    surface: '#E3E3E3',
    error: '#B00020',
    text: '#000000',
    disabled: '#949494',
    placeholder: '#B4B3B3',
    backdrop: 'rgba(0, 0, 0, 0.5)',
    notification: '#f50057',
    // Custom colors
    inputText: '#000000',
    strangerChatting: '#E3E3E3',
    userChatting: '#f7941d',
    icon: '#2F4D7F',
    textInputBackground: '#E3E3E3',
    actionBtn: '#5276AC',
  },
  roundness: 12,
};

export default theme;