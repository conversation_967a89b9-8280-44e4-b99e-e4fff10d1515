import {io} from 'socket.io-client';

import {backEndUrl, displayName} from '../../app.json';
import DeviceInfo from 'react-native-device-info';
import {notifyUser} from './notificationService';
import MessageStatus from '../Constants/MessageStatus';
import InitMessage from '../Constants/MessageTextConst';
let currentPartner;
let partnerId = -1;
let socket;
export let isRoomPageActive = true;
export const setIsRoomPage = flag => {
  isRoomPageActive = flag;
};
const SocketService = {
  init: (storedLinks, setLinks) => {
    socket = io(backEndUrl, {
      query: {deviceId: DeviceInfo.getUniqueIdSync(), channelId: displayName},
    });
    socket.on('tabsLinks', ({games, quiz}) => {
      let links = {...storedLinks};
      if (games) {
        links.games = games;
      }
      if (quiz) {
        links.quiz = quiz;
      }
      setLinks(links);
    });
  },
  ack: updateMessage => {
    socket.on('ack', msg => {
      updateMessage(msg.id, 'status', MessageStatus.sent);
    });
  },
  serverMessage: (addServerMessage, setPartner) => {
    socket.on('serverMessage', msg => {
      //todo: add disconnect to wipe partner
      if (msg.partner) {
        setPartner(msg.partner);
        partnerId++;
        currentPartner = msg.partner;
        delete msg.partner;
      }
      if (msg.source === 'user') {
        msg.partner = partnerId;
      }
      if (msg.text) {
        notifyUser(msg.text, isRoomPageActive);
        addServerMessage(msg);
      } else if (msg.attachment.type === 'image') {
        notifyUser('You have an Image', isRoomPageActive);
        addServerMessage({
          uri: msg.attachment.payload.url,
          source: msg.source,
          partner: partnerId,
        });
      }
    });
  },
  userTyping: userTyping => {
    socket.on('typing', msg => {
      userTyping(msg.typing.flag);
    });
  },
  disconnect: addServerMessage => {
    socket.on('disconnect', () =>
      addServerMessage({text: InitMessage.disconnected, source: 'sys'}),
    );
  },
  emit: (room, message, callback) => {
    socket.emit(room, message, callback);
  },
  typing: flag => {
    if (currentPartner?.sessionId) {
      socket.emit('typing', {sessionId: currentPartner.sessionId, flag});
    }
  },
  chat: msg => {
    if (currentPartner?.sessionId) {
      socket.emit('chat', {...msg, sessionId: currentPartner.sessionId});
    } else {
      socket.emit('chat message', msg);
    }
  },
};
export default SocketService;
