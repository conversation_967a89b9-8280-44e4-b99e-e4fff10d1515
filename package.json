{"name": "chat-client", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest", "android-release": "npx react-native build-android --mode=release", "android-clean": "cd android && rm -rf app/src/main/res/drawable-mdpi&& ./gradlew clean  && cd ../", "react-bundle": "react-native bundle --platform android --dev false --entry-file index.js --bundle-output ./android/app/src/main/assets/index.android.bundle --assets-dest android/app/src/main/res", "release": "npm run android-clean  && npm run android-release", "test-release": "rm -rf tmp && java -jar bundletool.jar build-apks --bundle=./android/app/build/outputs/bundle/release/app-release.aab --output=./tmp/my_app.apks --ks=./android/app/clone01.keystore --ks-pass=pass:45454545a --ks-key-alias=my-key-alias --key-pass=pass:45454545a && java -jar bundletool.jar install-apks --apks=./tmp/my_app.apks", "brt": "npm i && npm run release && npm run test-release", "postinstall": "patch-package"}, "dependencies": {"@babel/eslint-parser": "^7.26.5", "@bam.tech/react-native-image-resizer": "^3.0.11", "@manu_omg/react-native-emoji-selector": "^1.0.3", "@react-native-async-storage/async-storage": "^2.1.1", "@react-native-camera-roll/camera-roll": "^7.9.0", "@react-native-clipboard/clipboard": "^1.16.1", "@react-native-community/eslint-config": "^3.2.0", "@react-native-community/push-notification-ios": "^1.11.0", "@react-native-firebase/analytics": "^21.7.0", "@react-native-firebase/app": "^21.7.0", "@react-native-firebase/remote-config": "^21.7.0", "@react-navigation/material-top-tabs": "^7.1.0", "@react-navigation/native": "^7.0.14", "@react-navigation/native-stack": "^7.2.0", "@rnx-kit/align-deps": "^3.0.2", "babel-plugin-transform-remove-console": "^6.9.4", "deprecated-react-native-prop-types": "^5.0.0", "eslint-plugin-react-native": "^5.0.0", "leo-profanity": "^1.7.0", "metro-react-native-babel-preset": "^0.77.0", "patch-package": "^8.0.0", "react": "18.1.0", "react-native": "^0.76.6", "react-native-bigheads": "^1.1.0", "react-native-device-info": "^14.0.2", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "^2.22.0", "react-native-google-mobile-ads": "^14.8.0", "react-native-hyperlink": "^0.0.22", "react-native-iap": "^12.16.2", "react-native-image-marker": "^1.2.6", "react-native-image-zoom-viewer": "^3.0.1", "react-native-popup-menu": "^0.16.0", "react-native-pager-view": "^6.6.1", "react-native-paper": "^5.13.1", "react-native-pressable-opacity": "^1.0.10", "react-native-push-notification": "^8.1.1", "react-native-rate": "^1.2.12", "react-native-ratings": "^8.1.0", "react-native-reanimated": "^3.17.0-nightly", "react-native-safe-area-context": "^5.1.0", "react-native-screens": "^4.5.0", "react-native-splash-screen": "^3.3.0", "react-native-svg": "^15.11.1", "react-native-svg-transformer": "^1.5.0", "react-native-tab-view": "^4.0.5", "react-native-typing-animation": "^0.1.7", "react-native-vector-icons": "^10.2.0", "react-native-vision-camera": "^4.6.3", "react-native-walkthrough-tooltip": "^1.6.0", "react-native-webview": "^13.13.1", "recoil": "^0.7.7", "socket.io-client": "^4.8.1"}, "devDependencies": {"@babel/core": "^7.26.0", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.26.0", "@react-native-community/cli": "15.0.1", "@react-native-community/cli-platform-android": "15.0.1", "@react-native-community/cli-platform-ios": "15.0.1", "@react-native/babel-preset": "0.76.6", "@react-native/eslint-config": "0.76.6", "@react-native/metro-config": "0.76.6", "@react-native/typescript-config": "0.76.6", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.7.0", "eslint": "^9.18.0", "jest": "^29.7.0", "prettier": "2.8.8", "react-test-renderer": "^19.0.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}, "resolutions": {"react-native-svg": "15.11.1"}}